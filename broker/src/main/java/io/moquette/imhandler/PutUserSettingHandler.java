/*
 * This file is part of the Wildfire Chat package.
 * (c) Heavyrain2012 <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

package io.moquette.imhandler;

import cn.wildfirechat.common.ErrorCode;
import cn.wildfirechat.proto.ProtoConstants;
import cn.wildfirechat.proto.WFCMessage;
import com.google.gson.JsonObject;
import io.moquette.spi.impl.Qos1PublishHandler;
import io.netty.buffer.ByteBuf;
import win.liyufan.im.GsonUtil;
import win.liyufan.im.IMTopic;

import static win.liyufan.im.UserSettingScope.kUserSettingConversationSync;
import static win.liyufan.im.UserSettingScope.kUserSettingCustomState;

@Handler(IMTopic.PutUserSettingTopic)
public class PutUserSettingHandler extends IMHandler<WFCMessage.ModifyUserSettingReq> {
    @Override
    public ErrorCode action(ByteBuf ackPayload, String clientID, String fromUser, ProtoConstants.RequestSourceType requestSourceType, WFCMessage.ModifyUserSettingReq request, Qos1PublishHandler.IMCallback callback) {
        if (request.getValue().contains("-") && request.getKey().equals(request.getValue())) {
            String[] strings = request.getKey().split("-");
            long timestamp = System.currentTimeMillis();
            String conversationType = strings[0];
            String uid = strings[1];//消息发送者
            if (Integer.parseInt(conversationType) == ProtoConstants.ConversationType.ConversationType_Group) {
                String groupId = strings[2];//群
                WFCMessage.GroupMember groupMember = m_messagesStore.getGroupMember(groupId, fromUser);
                String extra = null;
                try {
                    extra = groupMember.getExtra();
                    if (!extra.isEmpty()) {
                        JsonObject jsonObject = GsonUtil.gson.fromJson(extra, JsonObject.class);
                        jsonObject.addProperty("readAt", timestamp);
                        extra = GsonUtil.gson.toJson(jsonObject);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (extra == null || extra.isEmpty()) {
                    extra = "{\"readAt\":" + timestamp + "}";
                }
                m_messagesStore.modifyGroupMemberExtra(fromUser, groupId, extra, fromUser, false);
                WFCMessage.ModifyUserSettingReq modifyUserSettingReq = WFCMessage.ModifyUserSettingReq.newBuilder().setScope(kUserSettingCustomState).setKey(conversationType + "-0-" + groupId).setValue(timestamp + "").build();
                m_messagesStore.updateUserSettings(uid, modifyUserSettingReq, clientID);
                return ErrorCode.ERROR_CODE_SUCCESS;
            }
            WFCMessage.ModifyUserSettingReq modifyUserSettingReq = WFCMessage.ModifyUserSettingReq.newBuilder().setScope(kUserSettingConversationSync).setKey(conversationType + "-0-" + fromUser).setValue(timestamp + "").build();
            m_messagesStore.updateUserSettings(uid, modifyUserSettingReq, clientID);
        } else {
            m_messagesStore.updateUserSettings(fromUser, request, clientID);
        }
        return ErrorCode.ERROR_CODE_SUCCESS;
    }
}
