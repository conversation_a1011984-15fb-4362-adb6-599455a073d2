package win.liyufan.im;

public interface UserSettingScope {
    int kUserSettingConversationSilent = 1;
    int kUserSettingGlobalSilent = 2;
    int kUserSettingHiddenNotificationDetail = 4;
    int kUserSettingConversationSync = 7;
    int kUserSettingMyChannels = 8;
    int kUserSettingListenedChannels = 9;
    int kUserSettingPCOnline = 10;
    int kUserSettingMuteWhenPCOnline = 15;
    int kUserSettingNoDisturbing = 17;
    int kUserSettingConversationClearMessage = 18;
    int kUserSettingConversationDraft = 19;
    int kUserSettingDisableSyncDraft = 20;
    int kUserSettingVoipSilent = 21;
    int kUserSettingPttReserved = 22;
    int kUserSettingCustomState = 23;
    int kUserSettingDisableSecretChat = 24;
    int kUserSettingPttSilent = 25;
    int kUserSettingGroupRemark = 26;
    int kUserSettingPrivacySearchable = 27;
    int kUserSettingAddFriendStrategy = 28;
    int kUserSettingScopeSyncBadge = 29;
}
