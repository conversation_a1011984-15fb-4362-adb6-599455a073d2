<?xml version ="1.0" encoding="UTF-8"?>

<!--    # 本配置文件为debug使用，修改这里不会在Release包中生效。Release中包含的配置文件在 ${Porject_Path}/distribution/src/main/resources目录下-->

<c3p0-config>
    <!-- This is default config! -->
    <default-config>
        <property name="initialPoolSize">10</property>
        <property name="maxIdleTime">30</property>
        <property name="maxPoolSize">100</property>
        <property name="minPoolSize">10</property>
        <property name="maxStatements">200</property>
    </default-config>

    <!-- This is my config for mysql-->
    <named-config name="mysql">
        <!-- 指定数据库连接源的基本属性 -->
        <!--MySQL数据库驱动程序-->
        <property name="driverClass">com.mysql.cj.jdbc.Driver</property>
        <!--MySQL数据库地址-->
        <property name="jdbcUrl">*********************************************************************************************************************************************************************************************</property>
        <!--MySQL数据库用户名-->
        <property name="user">root</property>
        <!--MySQL数据库密码-->
        <property name="password">123456</property>
        <!-- 初始化连接池中的连接数，取值应在minPoolSize与maxPoolSize之间，默认为3-->
        <property name="initialPoolSize">10</property>
        <!--最大空闲时间，60秒内未使用则连接被丢弃。若为0则永不丢弃。默认值: 0 -->
        <property name="maxIdleTime">30</property>
        <!--连接池中保留的最大连接数。默认值: 15 -->
        <property name="maxPoolSize">100</property>
        <!-- 连接池中保留的最小连接数，默认为：3-->
        <property name="minPoolSize">10</property>
        <!--c3p0全局的PreparedStatements缓存的大小。如果maxStatements与maxStatementsPerConnection均为0，则缓存不生效，只要有一个不为0，则语句的缓存就能生效。如果默认值: 0-->
        <property name="maxStatements">200</property>
        <!-- 当连接池连接耗尽时，客户端调用getConnection()后等待获取新连接的时间，超时后将抛出SQLException，如设为0则无限期等待。单位毫秒。默认: 0 -->
        <property name="checkoutTimeout">3000</property>
        <!--当连接池中的连接耗尽的时候c3p0一次同时获取的连接数。默认值: 3 -->
        <property name="acquireIncrement">2</property>
        <!--定义在从数据库获取新连接失败后重复尝试的次数。默认值: 30 ；小于等于0表示无限次-->
        <property name="acquireRetryAttempts">0</property>
        <!--重新尝试的时间间隔，默认为：1000毫秒-->
        <property name="acquireRetryDelay">1000</property>
        <!--关闭连接时，是否提交未提交的事务，默认为false，即关闭连接，回滚未提交的事务 -->
        <property name="autoCommitOnClose">false</property>
        <!--c3p0将建一张名为Test的空表，并使用其自带的查询语句进行测试。如果定义了这个参数那么属性preferredTestQuery将被忽略。你不能在这张Test表上进行任何操作，它将只供c3p0测试使用。默认值: null -->
        <!--<property name="automaticTestTable">null</property>-->
        <!--如果为false，则获取连接失败将会引起所有等待连接池来获取连接的线程抛出异常，但是数据源仍有效保留，并在下次调用getConnection()的时候继续尝试获取连接。如果设为true，那么在尝试获取连接失败后该数据源将申明已断开并永久关闭。默认: false-->
        <property name="breakAfterAcquireFailure">false</property>
        <!--每60秒检查所有连接池中的空闲连接。默认值: 0，不检查 -->
        <property name="idleConnectionTestPeriod">60</property>
        <!--maxStatementsPerConnection定义了连接池内单个连接所拥有的最大缓存statements数。默认值: 0 -->
        <!--<property name="maxStatementsPerConnection"></property>-->
    </named-config>
</c3p0-config>
